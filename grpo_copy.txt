===============================================================================
GRPO (Generalized Reward-based Policy Optimization) 完整算法伪代码
基于 TRL 库的完整实现，包含所有核心特性
===============================================================================

【算法初始化阶段】
初始化策略网络 π(y|x; θ)（当前训练的模型）
初始化参考网络 π_ref(y|x)（可选，用于KL正则化，通常是SFT模型的副本）
初始化奖励模型 R(x, y)（用于评估生成文本的质量）
初始化优化器 optimizer（通常是AdamW）

设置超参数：
    - β: KL正则化系数（默认0.0，如果>0则启用KL正则化）
    - ε_low, ε_high: 双边裁剪的下界和上界（默认0.2）
    - δ: 可选的单边裁剪上界（默认None）
    - num_generations: 每个prompt生成的候选数量（默认8）
    - importance_sampling_level: 重要性采样级别（"token"或"sequence"）
    - loss_type: 损失类型（"grpo", "bnpo", "dr_grpo"）
    - top_entropy_quantile: 高熵token筛选比例（默认1.0，即不筛选）
    - temperature: 生成温度（默认1.0）
    - max_completion_length: 最大生成长度

【主训练循环】
对于每个训练epoch do:
    对于每个训练batch do:
        
        ═══════════════════════════════════════════════════════════════════
        【第一阶段：生成和评分阶段】
        ═══════════════════════════════════════════════════════════════════
        
        1. 数据预处理：
            从训练数据集中采样一批prompts: {x₁, x₂, ..., x_B}
            应用聊天模板格式化prompts（如果需要）
            对prompts进行tokenization得到prompt_ids和prompt_mask
            如果max_prompt_length设置，则截断过长的prompts
        
        2. 多候选生成：
            对于每个prompt x_i do:
                使用当前策略π(y|x; θ)生成num_generations个候选回答:
                    {y_i1, y_i2, ..., y_i_num_generations}
                生成参数：
                    - 使用采样生成（do_sample=True）
                    - temperature = config.temperature
                    - top_p = config.top_p
                    - top_k = config.top_k
                    - max_length = config.max_completion_length
            
            注意：支持三种生成方式：
                - 标准transformers生成
                - vLLM加速生成（server模式或colocate模式）
                - transformers paged生成
        
        3. 奖励计算：
            对于每个(prompt, completion)对 (x_i, y_ij) do:
                计算奖励 r_ij = R(x_i, y_ij)
                支持多个奖励函数并行计算
                支持自定义奖励函数（函数式或模型式）
        
        4. 奖励归一化（按prompt组）：
            对于每个prompt组 {(x_i, y_i1, r_i1), ..., (x_i, y_i_num_generations, r_i_num_generations)} do:
                计算组内奖励均值: μ_i = mean({r_i1, r_i2, ..., r_i_num_generations})
                计算组内奖励标准差: σ_i = std({r_i1, r_i2, ..., r_i_num_generations})
                归一化奖励: r̃_ij = (r_ij - μ_i) / (σ_i + ε)
                设置优势值: advantages_ij = r̃_ij（GRPO直接使用归一化奖励作为优势）
        
        5. 计算旧策略概率（用于重要性采样）：
            如果是第一次迭代或steps_per_generation <= gradient_accumulation_steps:
                old_per_token_logps = current_per_token_logps.detach()
            否则:
                使用缓存的old_per_token_logps
        
        ═══════════════════════════════════════════════════════════════════
        【第二阶段：GRPO损失计算和参数更新】
        ═══════════════════════════════════════════════════════════════════
        
        6. 批次数据准备：
            将所有episodes按token长度排序（提高批处理效率）
            分割成micro_batches以适应内存限制
            
            对于每个micro_batch do:
                准备输入数据：
                    - input_ids = concat(prompt_ids, completion_ids)
                    - attention_mask = 相应的注意力掩码
                    - completion_mask = 标识生成token的掩码（只对生成部分计算损失）
                    - advantages = 对应的优势值
        
        7. 前向传播计算概率：
            使用当前策略π(y|x; θ)计算:
                logits = model(input_ids, attention_mask)
                per_token_logps = get_log_probabilities(logits, input_ids, completion_mask)
                
            可选：计算熵值（用于高熵token筛选）:
                if top_entropy_quantile < 1.0:
                    entropies = compute_entropy_from_logits(logits)
                    entropy_mask = get_high_entropy_mask(entropies, completion_mask, top_entropy_quantile)
        
        8. 重要性采样权重计算：
            计算对数概率比: log_ratio = per_token_logps - old_per_token_logps
            
            根据importance_sampling_level计算重要性权重:
                if importance_sampling_level == "token":
                    log_importance_weights = log_ratio  # shape: (B, T)
                elif importance_sampling_level == "sequence":
                    # 序列级别：对每个序列求平均
                    log_importance_weights = sum(log_ratio * completion_mask, dim=-1) / sum(completion_mask, dim=-1)
                    log_importance_weights = expand_dims(log_importance_weights, -1)  # shape: (B, 1)
        
        9. GRPO核心：双边裁剪机制：
            计算重要性权重: coef_1 = exp(log_importance_weights)
            计算裁剪权重: coef_2 = clamp(coef_1, 1-ε_low, 1+ε_high)
            
            可选：应用单边裁剪（如果δ设置）:
                if δ is not None:
                    coef_1 = clamp(coef_1, max=δ)
        
        10. GRPO损失计算：
            计算两个候选损失:
                per_token_loss1 = coef_1 * expand_dims(advantages, 1)
                per_token_loss2 = coef_2 * expand_dims(advantages, 1)
            
            应用PPO风格的双边裁剪:
                per_token_loss = -min(per_token_loss1, per_token_loss2)
            
            可选：应用高熵token掩码:
                if entropy_mask is not None:
                    per_token_loss = per_token_loss * entropy_mask
        
        11. 可选：KL正则化项：
            if β > 0 and reference_model is not None:
                使用参考模型计算:
                    ref_logits = reference_model(input_ids, attention_mask)
                    ref_per_token_logps = get_log_probabilities(ref_logits, input_ids, completion_mask)
                
                计算KL散度:
                    per_token_kl = exp(ref_per_token_logps - per_token_logps) 
                                 - (ref_per_token_logps - per_token_logps) - 1
                
                添加KL正则化项:
                    per_token_loss = per_token_loss + β * per_token_kl
        
        12. 损失聚合：
            根据loss_type进行不同的聚合方式:
                if loss_type == "grpo":
                    # 序列平均（不推荐，有长度偏差）
                    loss = mean(sum(per_token_loss * completion_mask, dim=-1) / sum(completion_mask, dim=-1))
                elif loss_type == "bnpo":
                    # 批次内token平均（推荐）
                    loss = sum(per_token_loss * completion_mask) / sum(completion_mask)
                elif loss_type == "dr_grpo":
                    # 密度比率版本（消除长度偏差）
                    loss = sum(per_token_loss * completion_mask) / (batch_size * max_completion_length)
        
        13. 反向传播和参数更新：
            loss.backward()  # 计算梯度
            clip_grad_norm_(model.parameters(), max_grad_norm)  # 梯度裁剪
            optimizer.step()  # 更新参数
            optimizer.zero_grad()  # 清零梯度
        
        14. 指标记录：
            记录训练指标:
                - 损失值
                - KL散度（如果启用）
                - 熵值
                - 裁剪比率（低裁剪、高裁剪、总裁剪）
                - 奖励统计（均值、标准差）
                - 生成长度统计
                - 梯度范数
        
        ═══════════════════════════════════════════════════════════════════
        【可选：参考模型同步】
        ═══════════════════════════════════════════════════════════════════
        
        15. 参考模型更新（如果启用sync_ref_model）：
            if sync_ref_model and step % sync_ref_model_every == 0:
                将当前策略模型的参数复制到参考模型
                reference_model.load_state_dict(policy_model.state_dict())

end for each batch
end for each epoch

===============================================================================
【关键算法特性总结】
===============================================================================

1. 在线学习：每个batch都重新生成数据，不重复使用旧数据
2. 重要性采样：支持token级别和sequence级别两种模式
3. 双边裁剪：防止策略更新过大，保持训练稳定性
4. KL正则化：可选的与参考模型的KL散度约束
5. 多种损失类型：支持GRPO、BNPO、DR_GRPO三种聚合方式
6. 高熵筛选：可选的高质量token筛选机制
7. 灵活的生成：支持多种生成后端（transformers、vLLM等）
8. 奖励归一化：按prompt组进行奖励标准化
9. 无Value Model：直接使用奖励作为优势，简化架构
10. 分布式支持：完整的多GPU训练支持

===============================================================================
【与传统PPO的主要区别】
===============================================================================

1. 无Value Model：GRPO不需要训练额外的价值函数
2. 在线生成：每步都生成新数据，而不是重复使用经验缓冲区
3. 灵活的重要性采样：支持token和sequence两个级别
4. 改进的损失聚合：提供多种方式消除长度偏差
5. 更简单的优势估计：直接使用归一化奖励







===============================================================================
非常好，这个问题直击整个训练过程的“发动机”部分。**每个 micro-batch 的核心任务是：为一小部分数据计算出一个损失值，并根据这个损失值计算出应该如何微调模型参数的“方向”（即梯度）。**

我们可以把整个第二阶段想象成一个工厂的流水线，而每个 micro-batch 就是送到流水线上的一个小零件。

下面是一个 micro-batch 在这个“流水线”上经历的完整生命周期：

---

### 一个微批次 (Micro-batch) 的生命周期

假设我们的【第一阶段】生成了 256 个 (prompt, completion, advantage, old_logps) 的数据样本。由于显存限制，我们设置 `gradient_accumulation_steps = 8`，这意味着我们将这 256 个样本分成 8 个 micro-batch，每个包含 32 个样本。

现在，我们来看**第一个 micro-batch**（包含32个样本）做了什么：

#### **第 1 步: 数据进入流水线 (对应伪代码步骤 6 & 7)**

*   **输入**: 这个 micro-batch 包含的 32 个样本的数据，主要包括：
    *   `input_ids` (prompt + completion 的 token ID)
    *   `attention_mask`
    *   `advantages` (这32个样本对应的归一化奖励)
    *   **以及最重要的**：我们仍然持有整个大批次的 `old_per_token_logps` 快照。

*   **动作**: 将 `input_ids` 和 `attention_mask` 输入到**当前**的策略模型 `π(θ)` 中。
*   **输出**:
    1.  `logits`: 模型对每个位置上所有词的原始打分。
    2.  `per_token_logps`: 模型**现在**认为生成这32个句子中每个 token 的对数概率是多少。

#### **第 2 步: 计算“策略变化” (对应伪代码步骤 8)**

*   **输入**: `per_token_logps` (来自第1步) 和 `old_per_token_logps` (来自“快照”)。
*   **动作**: 逐个 token 计算 `log_ratio = per_token_logps - old_per_token_logps`。
*   **输出**: `log_ratio` 张量。它告诉我们，对于这32个句子，新策略相对于旧策略的“偏好”变化了多少。

#### **第 3 步: 计算“经裁剪的损失” (对应伪代码步骤 9 & 10)**

*   **输入**: `log_ratio` (来自第2步) 和 `advantages` (来自第1步)。
*   **动作**:
    1.  计算权重 `coef_1 = exp(log_ratio)`。
    2.  计算裁剪后的权重 `coef_2 = clamp(coef_1, 1-ε, 1+ε)`。
    3.  计算两个候选损失：`loss1 = coef_1 * advantages` 和 `loss2 = coef_2 * advantages`。
    4.  取“更保守”的那个损失：`per_token_loss = -min(loss1, loss2)`。
*   **输出**: `per_token_loss` 张量。这是一个结合了“奖励信号”和“策略变化”的、经过安全裁剪的损失值。

#### **第 4 步: 加入“稳定缰绳” (对应伪代码步骤 11)**

*   **输入**: `per_token_loss` (来自第3步)，以及模型的 `per_token_logps` 和参考模型的 `ref_per_token_logps`。
*   **动作**: 如果 `β > 0`，计算 KL 散度惩罚项 `per_token_kl`，并将其加到 `per_token_loss` 上：`per_token_loss += β * per_token_kl`。
*   **输出**: 最终的、包含了所有考量的 `per_token_loss`。

#### **第 5 步: 汇总成一个数字 (对应伪代码步骤 12)**

*   **输入**: `per_token_loss` 张量。
*   **动作**: 将这32个样本中所有 token 的损失值加起来再求平均（例如，使用 `bnpo` 方式）。
*   **输出**: 一个**标量**（单个数字），我们称之为 `loss`。例如 `loss = -0.15`。

#### **第 6 步: 计算“调整方向” (对应伪代码步骤 13 的一部分)**

*   **输入**: `loss` 这个标量。
*   **动作**: 调用 `loss.backward()`。
*   **输出 (内部动作)**: PyTorch/TensorFlow 会自动计算出模型中**每一个参数**相对于这个 `loss` 的梯度。这些梯度值代表了“为了让 loss 变小，每个参数应该朝哪个方向移动一点点”。
*   **关键**: 这些计算出的梯度会**累积**起来。此时，**模型参数还没有被更新！** 我们只是算出了一个“调整建议”。

---

**这个 micro-batch 的工作到此结束。**

然后，流水线接收**第二个 micro-batch**，重复上述从第1步到第6步的**所有操作**。`loss.backward()` 计算出的新梯度会**加到**上一个 micro-batch 的梯度上。

这个过程会一直持续，直到所有 8 个 micro-batch 都处理完毕。

### **所有 Micro-batch 完成后**

当 8 个 micro-batch 的梯度全部累积完毕后，才执行最后一步：

*   **`optimizer.step()`**: 优化器根据累积的总梯度，**一次性地**对模型的参数进行更新。
*   **`optimizer.zero_grad()`**: 清空累积的梯度，为下一个大循环（下一个生成批次）做准备。

### 总结：每个 Micro-batch 做了什么？

| 动作 | 目的 |
| :--- | :--- |
| **前向传播 (Forward Pass)** | 获取当前模型对一小批数据的“看法”（`per_token_logps`）。 |
| **计算 `log_ratio`** | 比较当前模型和旧模型（快照）的“看法”差异。 |
| **计算裁剪损失** | 将奖励信号与策略变化结合，生成一个稳定、保守的损失信号。 |
| **聚合损失 (Aggregation)** | 将所有token的损失汇集成一个可供优化的单一数值 `loss`。 |
| **反向传播 (Backward Pass)** | 根据 `loss` 计算出模型参数的调整方向（梯度），并**累积**起来。 |

所以，每个 micro-batch 的工作就是为“最终的大更新”贡献一份自己的力量（梯度）。通过累积多个 micro-batch 的梯度，我们能以较小的显存，模拟出一次性处理一个大 batch 的效果，使得梯度估计更准确，训练更稳定。