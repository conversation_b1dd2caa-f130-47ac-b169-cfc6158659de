===============================================================================
GRPO 完整算法伪代码
基于 TRL 库的完整实现，包含所有核心特性
===============================================================================

【算法初始化阶段】
初始化策略网络 π(y|x; θ)（当前训练的模型）
初始化参考网络 π_ref(y|x)（可选，用于KL正则化，通常是SFT模型的副本）
初始化奖励模型 R(x, y)（用于评估生成文本的质量）
初始化优化器 optimizer（通常是AdamW）

设置超参数：
    - β: KL正则化系数（默认0.0，如果>0则启用KL正则化）
    - ε_low, ε_high: 双边裁剪的下界和上界（默认0.2）
    - δ: 可选的单边裁剪上界（默认None）
    - num_generations: 每个prompt生成的候选数量（默认8）
    - importance_sampling_level: 重要性采样级别（"token"或"sequence"）
    - loss_type: 损失类型（"grpo", "bnpo", "dr_grpo"）
    - top_entropy_quantile: 高熵token筛选比例（默认1.0，即不筛选）
    - temperature: 生成温度（默认1.0）
    - max_completion_length: 最大生成长度

【主训练循环：Outer Loop 和 Inner Loop 结构】
═══════════════════════════════════════════════════════════════════

【Outer Loop：数据生成循环】
# 每 generate_every = steps_per_generation * num_iterations 步执行一次
for generation_cycle in range(total_training_steps // generate_every):

    ═══════════════════════════════════════════════════════════════════
    【第一阶段：生成和评分阶段】（在Outer Loop中执行）
    ═══════════════════════════════════════════════════════════════════

    1. 数据预处理：
        从训练数据集中采样一批prompts: {x₁, x₂, ..., x_B}
        应用聊天模板格式化prompts（如果需要）
        对prompts进行tokenization得到prompt_ids和prompt_mask
        如果max_prompt_length设置，则截断过长的prompts

    2. 多候选生成：
        对于每个prompt x_i do:
            使用当前策略π(y|x; θ)生成num_generations个候选回答:
                {y_i1, y_i2, ..., y_i_num_generations}
            生成参数：
                - 使用采样生成（do_sample=True）
                - temperature = config.temperature
                - top_p = config.top_p
                - top_k = config.top_k
                - max_length = config.max_completion_length

        注意：支持三种生成方式：
            - 标准transformers生成
            - vLLM加速生成（server模式或colocate模式）
            - transformers paged生成

    3. 奖励计算：
        对于每个(prompt, completion)对 (x_i, y_ij) do:
            计算奖励 r_ij = R(x_i, y_ij)
            支持多个奖励函数并行计算
            支持自定义奖励函数（函数式或模型式）

    4. 奖励归一化（按prompt组）：
        对于每个prompt组 {(x_i, y_i1, r_i1), ..., (x_i, y_i_num_generations, r_i_num_generations)} do:
            计算组内奖励均值: μ_i = mean({r_i1, r_i2, ..., r_i_num_generations})
            计算组内奖励标准差: σ_i = std({r_i1, r_i2, ..., r_i_num_generations})
            归一化奖励: r̃_ij = (r_ij - μ_i) / (σ_i + ε)
            设置优势值: advantages_ij = r̃_ij（GRPO直接使用归一化奖励作为优势）

    5. 计算旧策略概率（用于重要性采样）：
        【重要性采样的核心机制】
        # 判断是否需要计算old_per_token_logps
        generate_every = steps_per_generation * num_iterations

        if gradient_accumulation_steps % generate_every != 0:
            # 生成和优化步骤不对齐，需要重要性采样
            with torch.no_grad():
                old_per_token_logps = compute_log_probabilities(
                    current_model, input_ids, attention_mask, logits_to_keep
                ).detach()  # 固定当前模型的概率作为"旧策略"
        else:
            # 生成和优化步骤对齐，可以跳过重要性采样
            old_per_token_logps = None  # 后续会用current_logps.detach()代替

        【缓存机制说明】
        # TRL库使用智能缓存：
        # - 每 generate_every 步重新生成completions
        # - 在这期间重复使用相同的old_per_token_logps
        # - 这样避免了每步都重新计算，提高效率

    6. 批次数据分割和缓存：
        将生成的大批次分割为steps_per_generation个子批次
        缓存这些子批次供后续的Inner Loop使用

    【Inner Loop：策略优化循环】
    # 对同一批生成的数据进行多次优化
    for iteration in range(num_iterations):  # μ 参数，默认为1
        for step in range(steps_per_generation):  # 使用缓存数据的步数
        
            ═══════════════════════════════════════════════════════════════════
            【第二阶段：GRPO损失计算和参数更新】（在Inner Loop中执行）
            ═══════════════════════════════════════════════════════════════════

            7. 获取当前步骤的缓存数据：

                # 从缓存中获取当前步骤的数据
                current_batch = buffered_inputs[step % steps_per_generation]

                # Micro-batch数据准备
                将current_batch按token长度排序（提高批处理效率）
                分割成micro_batches以适应内存限制

                对于每个micro_batch do:
                    准备输入数据：
                        - input_ids = concat(prompt_ids, completion_ids)
                        - attention_mask = 相应的注意力掩码
                        - completion_mask = 标识生成token的掩码（只对生成部分计算损失）
                        - advantages = 对应的优势值（已按组归一化）
                        - old_per_token_logps = 旧策略概率（用于重要性采样）
        
            8. 前向传播计算概率：
                使用当前策略π(y|x; θ)计算:
                    logits = model(input_ids, attention_mask)
                    per_token_logps = get_log_probabilities(logits, input_ids, completion_mask)

                可选：计算熵值（用于高熵token筛选）:
                    if top_entropy_quantile < 1.0:
                        entropies = compute_entropy_from_logits(logits)
                        entropy_mask = get_high_entropy_mask(entropies, completion_mask, top_entropy_quantile)

            9. 重要性采样权重计算：
                计算对数概率比: log_ratio = per_token_logps - old_per_token_logps

                根据importance_sampling_level计算重要性权重:
                    if importance_sampling_level == "token":
                        log_importance_weights = log_ratio  # shape: (B, T)
                    elif importance_sampling_level == "sequence":
                        # 序列级别：对每个序列求平均
                        log_importance_weights = sum(log_ratio * completion_mask, dim=-1) / sum(completion_mask, dim=-1)
                        log_importance_weights = expand_dims(log_importance_weights, -1)  # shape: (B, 1)

            10. GRPO核心：双边裁剪机制：
                计算重要性权重: coef_1 = exp(log_importance_weights)
                计算裁剪权重: coef_2 = clamp(coef_1, 1-ε_low, 1+ε_high)

                可选：应用单边裁剪（如果δ设置）:
                    if δ is not None:
                        coef_1 = clamp(coef_1, max=δ)

            11. GRPO损失计算：
                计算两个候选损失:
                    per_token_loss1 = coef_1 * expand_dims(advantages, 1)
                    per_token_loss2 = coef_2 * expand_dims(advantages, 1)

                应用PPO风格的双边裁剪:
                    per_token_loss = -min(per_token_loss1, per_token_loss2)

                可选：应用高熵token掩码:
                    if entropy_mask is not None:
                        per_token_loss = per_token_loss * entropy_mask

            12. 可选：KL正则化项：
                if β > 0 and reference_model is not None:
                    使用参考模型计算:
                        ref_logits = reference_model(input_ids, attention_mask)
                        ref_per_token_logps = get_log_probabilities(ref_logits, input_ids, completion_mask)

                    计算KL散度:
                        per_token_kl = exp(ref_per_token_logps - per_token_logps)
                                     - (ref_per_token_logps - per_token_logps) - 1

                    添加KL正则化项:
                        per_token_loss = per_token_loss + β * per_token_kl

            13. 损失聚合：
                根据loss_type进行不同的聚合方式:
                    if loss_type == "grpo":
                        # 序列平均（不推荐，有长度偏差）
                        loss = mean(sum(per_token_loss * completion_mask, dim=-1) / sum(completion_mask, dim=-1))
                    elif loss_type == "bnpo":
                        # 批次内token平均（推荐）
                        loss = sum(per_token_loss * completion_mask) / sum(completion_mask)
                    elif loss_type == "dr_grpo":
                        # 密度比率版本（消除长度偏差）
                        loss = sum(per_token_loss * completion_mask) / (batch_size * max_completion_length)

            14. 反向传播和参数更新：
                loss.backward()  # 计算梯度
                clip_grad_norm_(model.parameters(), max_grad_norm)  # 梯度裁剪
                optimizer.step()  # 更新参数
                optimizer.zero_grad()  # 清零梯度
        
        14. 指标记录：
            记录训练指标:
                - 损失值
                - KL散度（如果启用）
                - 熵值
                - 裁剪比率（低裁剪、高裁剪、总裁剪）
                - 奖励统计（均值、标准差）
                - 生成长度统计
                - 梯度范数
        
        ═══════════════════════════════════════════════════════════════════
        【参考模型管理策略（基于TRL库实现）】
        ═══════════════════════════════════════════════════════════════════

        15. 参考模型初始化和管理：

            【参考模型加载策略】
            if beta == 0.0:
                # 无KL正则化，不加载参考模型以节省显存
                ref_model = None
            elif is_peft_model(model):
                # 使用PEFT时，可以通过禁用adapter回到初始模型
                ref_model = None  # 不需要单独的参考模型
            else:
                # 标准情况：从相同checkpoint加载独立的参考模型
                ref_model = AutoModelForCausalLM.from_pretrained(model_id)
                ref_model.eval()  # 设为评估模式
                for param in ref_model.parameters():
                    param.requires_grad = False  # 冻结参数

            【可选：TR-DPO风格的参考模型同步】
            if sync_ref_model:
                # 每ref_model_sync_steps步同步一次
                if step % ref_model_sync_steps == 0:
                    # 混合更新：π_ref = α * π_θ + (1 - α) * π_ref_prev
                    for ref_param, policy_param in zip(ref_model.parameters(), policy_model.parameters()):
                        ref_param.data = (ref_model_mixup_alpha * policy_param.data +
                                        (1 - ref_model_mixup_alpha) * ref_param.data)

        end for each step

    end for each iteration

end for each generation_cycle

===============================================================================
【关键算法特性总结】
===============================================================================

1. 在线学习：每个batch都重新生成数据，不重复使用旧数据
2. 重要性采样：支持token级别和sequence级别两种模式
3. 双边裁剪：防止策略更新过大，保持训练稳定性
4. KL正则化：可选的与参考模型的KL散度约束
5. 多种损失类型：支持GRPO、BNPO、DR_GRPO三种聚合方式
6. 高熵筛选：可选的高质量token筛选机制
7. 灵活的生成：支持多种生成后端（transformers、vLLM等）
8. 奖励归一化：按prompt组进行奖励标准化
9. 无Value Model：直接使用奖励作为优势，简化架构
10. 分布式支持：完整的多GPU训练支持

===============================================================================
【与传统PPO的主要区别】
===============================================================================

1. 无Value Model：GRPO不需要训练额外的价值函数
2. 在线生成：每步都生成新数据，而不是重复使用经验缓冲区
3. 灵活的重要性采样：支持token和sequence两个级别
4. 改进的损失聚合：提供多种方式消除长度偏差
5. 更简单的优势估计：直接使用归一化奖励







===============================================================================

for generation_cycle in range(total_training_steps // generate_every):
    ├── 第一阶段：生成和评分阶段（在Outer Loop中执行）
    │   ├── 1. 数据预处理
    │   ├── 2. 多候选生成
    │   ├── 3. 奖励计算
    │   ├── 4. 奖励归一化（按prompt组）
    │   ├── 5. 计算旧策略概率（用于重要性采样）
    │   └── 6. 批次数据分割和缓存
    │
    └── Inner Loop（策略优化循环）
        for iteration in range(num_iterations):  # μ 参数
            for step in range(steps_per_generation):
                ├── 第二阶段：GRPO损失计算和参数更新（在Inner Loop中执行）
                │   ├── 7. 获取当前步骤的缓存数据
                │   ├── 8. 前向传播计算概率
                │   ├── 9. 重要性采样权重计算
                │   ├── 10. GRPO核心：双边裁剪机制
                │   ├── 11. GRPO损失计算
                │   ├── 12. 可选：KL正则化项
                │   ├── 13. 损失聚合
                │   └── 14. 反向传播和参数更新